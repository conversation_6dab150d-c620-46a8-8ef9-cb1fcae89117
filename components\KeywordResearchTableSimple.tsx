import React from 'react';

interface KeywordResearchItem {
  mainKeyword: string;
  type: string;
  keyword: string;
  msv: number;
  searchIntent: string;
  kwDifficulty: number | string;
  competition: number;
  cpc: number;
  answer: string;
  timestamp: string;
}

interface KeywordResearchTableSimpleProps {
  data: KeywordResearchItem[];
  isLoading?: boolean;
}

const KeywordResearchTableSimple: React.FC<KeywordResearchTableSimpleProps> = ({ data, isLoading = false }) => {
  if (isLoading) {
    return (
      <div className="bg-white shadow-md rounded-lg p-6 mb-8">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Keyword Research</h2>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded mb-4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="bg-white shadow-md rounded-lg p-6 mb-8">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Keyword Research</h2>
        <p className="text-gray-500 italic">No data available. Please submit the form to generate keyword research.</p>
      </div>
    );
  }

  const getDifficultyDisplay = (difficulty: number | string) => {
    if (typeof difficulty === 'string') {
      return difficulty.toUpperCase();
    }
    if (difficulty <= 1.5) return 'LOW';
    if (difficulty <= 2.5) return 'MEDIUM';
    return 'HIGH';
  };

  const getDifficultyColor = (difficulty: number | string) => {
    const display = getDifficultyDisplay(difficulty);
    switch (display) {
      case 'LOW': return 'bg-green-100 text-green-800';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800';
      case 'HIGH': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getIntentColor = (intent: string) => {
    switch (intent?.toLowerCase()) {
      case 'transactional': return 'bg-blue-100 text-blue-800';
      case 'commercial': return 'bg-purple-100 text-purple-800';
      case 'informational': return 'bg-green-100 text-green-800';
      case 'navigational': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="bg-white shadow-md rounded-lg p-6 mb-8">
      <h2 className="text-xl font-semibold text-gray-800 mb-4">Keyword Research</h2>
      <div className="text-sm text-gray-600 mb-4">
        Showing {data.length} keywords
      </div>

      {/* Desktop view - traditional table */}
      <div className="hidden md:block overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">S.No.</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Main Keyword</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Keyword</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MSV</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Search Intent</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">KW Difficulty</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Competition</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CPC</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Answer</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timestamp</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {data.map((item, index) => (
              <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">{index + 1}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.mainKeyword}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.type}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.keyword}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.msv?.toLocaleString() || '0'}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getIntentColor(item.searchIntent)}`}>
                    {item.searchIntent}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(item.kwDifficulty)}`}>
                    {getDifficultyDisplay(item.kwDifficulty)}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.competition?.toFixed(2) || '0.00'}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${item.cpc?.toFixed(2) || '0.00'}</td>
                <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate" title={item.answer}>
                  {item.answer && item.answer.length > 50 ? `${item.answer.substring(0, 50)}...` : item.answer || ''}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.timestamp}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Mobile view - card layout */}
      <div className="md:hidden space-y-4">
        <div className="text-sm text-gray-500 mb-2 flex justify-between items-center">
          <span>Showing {data.length} results</span>
          <div className="text-indigo-600 text-xs">
            Swipe cards horizontally to see more →
          </div>
        </div>

        {data.map((item, index) => (
          <div key={index} className="bg-white border border-gray-200 rounded-lg shadow-sm p-4">
            <div className="flex justify-between items-center mb-3">
              <div className="flex items-center space-x-2">
                <span className="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded-full font-bold">#{index + 1}</span>
                <h3 className="font-medium text-gray-900">{item.keyword}</h3>
              </div>
              <span className="text-xs bg-indigo-100 text-indigo-800 px-2 py-1 rounded-full">{item.type}</span>
            </div>

            <div className="grid grid-cols-2 gap-3 text-sm">
              <div>
                <span className="block text-gray-500 text-xs">Main Keyword</span>
                <span className="font-medium">{item.mainKeyword}</span>
              </div>
              <div>
                <span className="block text-gray-500 text-xs">MSV</span>
                <span className="font-medium">{item.msv?.toLocaleString() || '0'}</span>
              </div>
              <div>
                <span className="block text-gray-500 text-xs">Search Intent</span>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getIntentColor(item.searchIntent)}`}>
                  {item.searchIntent}
                </span>
              </div>
              <div>
                <span className="block text-gray-500 text-xs">KW Difficulty</span>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(item.kwDifficulty)}`}>
                  {getDifficultyDisplay(item.kwDifficulty)}
                </span>
              </div>
              <div>
                <span className="block text-gray-500 text-xs">Competition</span>
                <span className="font-medium">{item.competition?.toFixed(2) || '0.00'}</span>
              </div>
              <div>
                <span className="block text-gray-500 text-xs">CPC</span>
                <span className="font-medium">${item.cpc?.toFixed(2) || '0.00'}</span>
              </div>
            </div>

            {item.answer && (
              <div className="mt-3">
                <span className="block text-gray-500 text-xs">Answer</span>
                <span className="text-sm text-gray-900">{item.answer}</span>
              </div>
            )}

            <div className="mt-3">
              <span className="block text-gray-500 text-xs">Timestamp</span>
              <span className="text-sm text-gray-900">{item.timestamp}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default KeywordResearchTableSimple;
