'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import AppLayout from '@/components/AppLayout';
import KeywordResearchTableSimple from '@/components/KeywordResearchTableSimple';
import ContentMatrixTableSimple from '@/components/ContentMatrixTableSimple';
import MatrixKPIs from '@/components/MatrixKPIs';
import ExportDropdown from '@/components/ExportDropdown';
import { Project, Matrix } from '@/types';
import { useAuth } from '@/context/AuthContext';
import { generateMockWebhookData } from '@/utils/mockData';
import { extractKeywordFromMatrixId, isValidMatrixId } from '@/utils/matrixUtils';


interface MatrixPageProps {
  params: {
    slug: string;
    matrixId: string;
  };
}

export default function MatrixPage({ params }: MatrixPageProps) {
  const [project, setProject] = useState<Project | null>(null);
  const [matrix, setMatrix] = useState<Matrix | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!user) {
      router.push('/login-with-otp');
      return;
    }

    const fetchData = async () => {
      try {
        setIsLoading(true);

        console.log('🔍 Fetching data for:', { slug: params.slug, matrixId: params.matrixId });

        // Check if matrixId is valid
        if (!isValidMatrixId(params.matrixId)) {
          console.error('❌ MatrixId is undefined or invalid:', params.matrixId);
          setError('Invalid matrix ID. Please check the URL.');
          setIsLoading(false);
          return;
        }

        // Fetch project by slug
        const projectResponse = await fetch(`/api/projects/slug/${params.slug}`);
        if (!projectResponse.ok) {
          throw new Error('Failed to fetch project');
        }

        const { project: fetchedProject } = await projectResponse.json();
        setProject(fetchedProject);

        // Try to fetch matrix from API first
        try {
          console.log('🔍 Fetching matrix with ID:', params.matrixId);
          const matrixResponse = await fetch(`/api/matrices/${params.matrixId}`);
          if (matrixResponse.ok) {
            const { matrix: fetchedMatrix } = await matrixResponse.json();
            console.log('✅ Matrix found in database:', fetchedMatrix);
            // Convert to expected format
            const formattedMatrix: Matrix = {
              _id: fetchedMatrix._id,
              projectId: fetchedMatrix.projectId,
              userId: fetchedMatrix.userId,
              mainKeyword: fetchedMatrix.mainKeyword,
              filename: fetchedMatrix.filename,
              location: fetchedMatrix.location,
              language: fetchedMatrix.language,
              keywordResearch: fetchedMatrix.keywordResearch,
              contentMatrix: fetchedMatrix.contentMatrix,
              createdAt: fetchedMatrix.createdAt,
              updatedAt: fetchedMatrix.updatedAt,
            };
            setMatrix(formattedMatrix);
            return;
          }
        } catch (matrixError) {
          console.log('⚠️ Matrix not found in database, generating mock data:', matrixError);
        }

        // Fallback to mock data if matrix not found
        // Extract keyword from matrix ID
        const keyword = extractKeywordFromMatrixId(params.matrixId);

        const mockData = generateMockWebhookData(keyword, 'United States', 'English');
        const mockMatrix: Matrix = {
          _id: params.matrixId,
          projectId: fetchedProject._id,
          userId: user.id,
          mainKeyword: keyword,
          filename: `${keyword.toLowerCase().replace(/\s+/g, '-')}-matrix.json`,
          location: 'United States',
          language: 'English',
          keywordResearch: mockData.keywordResearch,
          contentMatrix: mockData.contentMatrix,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        setMatrix(mockMatrix);
        console.log('📊 Mock matrix data loaded:', {
          keywordResearchCount: mockMatrix.keywordResearch?.length || 0,
          contentMatrixCount: mockMatrix.contentMatrix?.length || 0,
          mockMatrix
        });
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load matrix data. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [user, params.slug, params.matrixId, router]);

  if (isLoading) {
    return (
      <AppLayout>
        <div className="space-y-8">
          <div className="bg-white shadow-md rounded-lg p-6">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded mb-4 w-1/3"></div>
              <div className="h-4 bg-gray-200 rounded mb-6 w-1/2"></div>
              <div className="h-64 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-8">
        {project && matrix ? (
          <>
            <div className="bg-white shadow-md rounded-lg p-6">
              <div className="flex justify-between items-start mb-6">
                <div>
                  <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-2">
                    <Link href="/projects" className="hover:text-gray-700">Projects</Link>
                    <span>/</span>
                    <Link href={`/project/${params.slug}`} className="hover:text-gray-700">{project.name}</Link>
                    <span>/</span>
                    <span className="text-gray-900">{matrix.mainKeyword.toUpperCase()}</span>
                  </nav>
                  <h1 className="text-2xl font-bold text-gray-900 mb-2">

                    {matrix.mainKeyword.toLowerCase().replace(/\b\w/g, char => char.toUpperCase())}
                  </h1>
                  <p className="text-gray-600">
                    Generated on {new Date(matrix.createdAt).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </p>
                </div>
                <div className="flex space-x-3">
                  <ExportDropdown
                    exportData={{
                      keywordResearch: matrix.keywordResearch,
                      contentMatrix: matrix.contentMatrix,
                      projectName: project.name,
                      mainKeyword: matrix.mainKeyword,
                      location: matrix.location,
                      language: matrix.language,
                      exportDate: new Date().toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })
                    }}
                  />
                </div>
              </div>
            </div>

            {error && (
              <div className="bg-red-50 border-l-4 border-red-500 p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-700">{error}</p>
                  </div>
                </div>
              </div>
            )}

            <MatrixKPIs
              keywordData={matrix.keywordResearch}
              contentData={matrix.contentMatrix}
              mainKeyword={matrix.mainKeyword}
              location={matrix.location}
              language={matrix.language}
            />

            <ContentMatrixTableSimple
              data={matrix.contentMatrix || []}
              isLoading={false}
            />

            <KeywordResearchTableSimple
              data={matrix.keywordResearch || []}
              isLoading={false}
            />
          </>
        ) : (
          <div className="bg-white shadow-md rounded-lg p-6 text-center">
            <h2 className="text-xl font-semibold text-gray-800 mb-2">Matrix not found</h2>
            <p className="text-gray-600 mb-6">The matrix you're looking for doesn't exist or you don't have access to it.</p>
          </div>
        )}
      </div>
    </AppLayout>
  );
}
