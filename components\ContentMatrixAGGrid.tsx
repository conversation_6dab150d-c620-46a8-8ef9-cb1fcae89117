import React, { useMemo } from 'react';
import { AgGridReact } from 'ag-grid-react';
import { ColDef } from 'ag-grid-community';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';

interface ContentMatrixItem {
  mainKeyword: string;
  cluster: string;
  contentType: string;
  focus: string;
  category: string;
  url: string;
  searchVol: number;
  keywords: string;
  status: string;
}

interface ContentMatrixAGGridProps {
  data: ContentMatrixItem[];
  isLoading?: boolean;
}

const ContentMatrixAGGrid: React.FC<ContentMatrixAGGridProps> = ({ data, isLoading = false }) => {
  // Add serial numbers to data
  const rowData = useMemo(() => {
    return data.map((item, index) => ({
      serialNo: index + 1,
      ...item,
    }));
  }, [data]);

  // Column definitions
  const columnDefs: ColDef[] = useMemo(() => [
    {
      headerName: 'S.No.',
      field: 'serialNo',
      width: 80,
      pinned: 'left',
      cellStyle: { fontWeight: 'bold' },
    },
    {
      headerName: 'Main Keyword',
      field: 'mainKeyword',
      width: 150,
      filter: true,
      sortable: true,
    },
    {
      headerName: 'Cluster',
      field: 'cluster',
      width: 150,
      filter: true,
      sortable: true,
    },
    {
      headerName: 'Content Type',
      field: 'contentType',
      width: 130,
      filter: true,
      sortable: true,
    },
    {
      headerName: 'Focus',
      field: 'focus',
      width: 200,
      filter: true,
      sortable: true,
      wrapText: true,
      autoHeight: true,
    },
    {
      headerName: 'Category',
      field: 'category',
      width: 120,
      filter: true,
      sortable: true,
    },
    {
      headerName: 'URL',
      field: 'url',
      width: 200,
      filter: true,
      sortable: true,
      cellRenderer: (params: any) => {
        if (params.value) {
          return `<a href="${params.value}" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:text-blue-800 underline">${params.value}</a>`;
        }
        return params.value;
      },
    },
    {
      headerName: 'Search Volume',
      field: 'searchVol',
      width: 130,
      filter: 'agNumberColumnFilter',
      sortable: true,
      cellRenderer: (params: any) => {
        return params.value ? params.value.toLocaleString() : '0';
      },
    },
    {
      headerName: 'Keywords',
      field: 'keywords',
      width: 200,
      filter: true,
      sortable: true,
      wrapText: true,
      autoHeight: true,
    },
    {
      headerName: 'Status',
      field: 'status',
      width: 100,
      filter: true,
      sortable: true,
      cellRenderer: (params: any) => {
        const status = params.value;
        let className = 'px-2 py-1 rounded-full text-xs font-medium ';
        
        switch (status) {
          case 'Published':
            className += 'bg-green-100 text-green-800';
            break;
          case 'Draft':
            className += 'bg-yellow-100 text-yellow-800';
            break;
          case 'Planned':
            className += 'bg-blue-100 text-blue-800';
            break;
          default:
            className += 'bg-gray-100 text-gray-800';
        }
        
        return `<span class="${className}">${status}</span>`;
      },
    },
  ], []);

  // Default column properties
  const defaultColDef = useMemo(() => ({
    resizable: true,
    sortable: true,
    filter: true,
  }), []);

  if (isLoading) {
    return (
      <div className="bg-white shadow-md rounded-lg p-6 mb-8">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Content Cluster</h2>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded mb-4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="bg-white shadow-md rounded-lg p-6 mb-8">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Content Cluster</h2>
        <p className="text-gray-500 italic">No data available. Please submit the form to generate content matrix.</p>
      </div>
    );
  }

  return (
    <div className="bg-white shadow-md rounded-lg p-6 mb-8">
      <h2 className="text-xl font-semibold text-gray-800 mb-4">Content Cluster</h2>
      
      <div className="ag-theme-alpine" style={{ height: '500px', width: '100%' }}>
        <AgGridReact
          rowData={rowData}
          columnDefs={columnDefs}
          defaultColDef={defaultColDef}
          pagination={true}
          paginationPageSize={20}
          domLayout="normal"
          suppressCellFocus={true}
          rowSelection="multiple"
          animateRows={true}
          enableCellTextSelection={true}
          ensureDomOrder={true}
        />
      </div>
    </div>
  );
};

export default ContentMatrixAGGrid;
