import React, { useMemo } from 'react';
import { AgGridReact } from 'ag-grid-react';
import { ColDef } from 'ag-grid-community';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';

interface KeywordResearchItem {
  mainKeyword: string;
  type: string;
  keyword: string;
  msv: number;
  searchIntent: string;
  kwDifficulty: number | string;
  competition: number;
  cpc: number;
  answer: string;
  timestamp: string;
}

interface KeywordResearchAGGridProps {
  data: KeywordResearchItem[];
  isLoading?: boolean;
}

const KeywordResearchAGGrid: React.FC<KeywordResearchAGGridProps> = ({ data, isLoading = false }) => {
  // Add serial numbers to data
  const rowData = useMemo(() => {
    return data.map((item, index) => ({
      serialNo: index + 1,
      ...item,
    }));
  }, [data]);

  // Column definitions
  const columnDefs: ColDef[] = useMemo(() => [
    {
      headerName: 'S.No.',
      field: 'serialNo',
      width: 80,
      pinned: 'left',
      cellStyle: { fontWeight: 'bold' },
    },
    {
      headerName: 'Main Keyword',
      field: 'mainKeyword',
      width: 150,
      filter: true,
      sortable: true,
    },
    {
      headerName: 'Type',
      field: 'type',
      width: 100,
      filter: true,
      sortable: true,
    },
    {
      headerName: 'Keyword',
      field: 'keyword',
      width: 180,
      filter: true,
      sortable: true,
    },
    {
      headerName: 'MSV',
      field: 'msv',
      width: 100,
      filter: 'agNumberColumnFilter',
      sortable: true,
      cellRenderer: (params: any) => {
        return params.value ? params.value.toLocaleString() : '0';
      },
    },
    {
      headerName: 'Search Intent',
      field: 'searchIntent',
      width: 130,
      filter: true,
      sortable: true,
      cellRenderer: (params: any) => {
        const intent = params.value;
        let className = 'px-2 py-1 rounded-full text-xs font-medium ';
        
        switch (intent?.toLowerCase()) {
          case 'transactional':
            className += 'bg-blue-100 text-blue-800';
            break;
          case 'commercial':
            className += 'bg-purple-100 text-purple-800';
            break;
          case 'informational':
            className += 'bg-green-100 text-green-800';
            break;
          case 'navigational':
            className += 'bg-orange-100 text-orange-800';
            break;
          default:
            className += 'bg-gray-100 text-gray-800';
        }
        
        return `<span class="${className}">${intent}</span>`;
      },
    },
    {
      headerName: 'KW Difficulty',
      field: 'kwDifficulty',
      width: 120,
      filter: true,
      sortable: true,
      cellRenderer: (params: any) => {
        const difficulty = params.value;
        let className = 'px-2 py-1 rounded-full text-xs font-medium ';
        let displayValue = difficulty;
        
        if (typeof difficulty === 'string') {
          switch (difficulty.toUpperCase()) {
            case 'LOW':
              className += 'bg-green-100 text-green-800';
              break;
            case 'MEDIUM':
              className += 'bg-yellow-100 text-yellow-800';
              break;
            case 'HIGH':
              className += 'bg-red-100 text-red-800';
              break;
            default:
              className += 'bg-gray-100 text-gray-800';
          }
        } else {
          // Convert numeric to string representation
          if (difficulty <= 1.5) {
            displayValue = 'LOW';
            className += 'bg-green-100 text-green-800';
          } else if (difficulty <= 2.5) {
            displayValue = 'MEDIUM';
            className += 'bg-yellow-100 text-yellow-800';
          } else {
            displayValue = 'HIGH';
            className += 'bg-red-100 text-red-800';
          }
        }
        
        return `<span class="${className}">${displayValue}</span>`;
      },
    },
    {
      headerName: 'Competition',
      field: 'competition',
      width: 110,
      filter: 'agNumberColumnFilter',
      sortable: true,
      cellRenderer: (params: any) => {
        return params.value ? params.value.toFixed(2) : '0.00';
      },
    },
    {
      headerName: 'CPC',
      field: 'cpc',
      width: 100,
      filter: 'agNumberColumnFilter',
      sortable: true,
      cellRenderer: (params: any) => {
        return params.value ? `$${params.value.toFixed(2)}` : '$0.00';
      },
    },
    {
      headerName: 'Answer',
      field: 'answer',
      width: 200,
      filter: true,
      sortable: true,
      wrapText: true,
      autoHeight: true,
      cellRenderer: (params: any) => {
        const answer = params.value;
        if (answer && answer.length > 100) {
          return `<div title="${answer}">${answer.substring(0, 100)}...</div>`;
        }
        return answer || '';
      },
    },
    {
      headerName: 'Timestamp',
      field: 'timestamp',
      width: 150,
      filter: 'agDateColumnFilter',
      sortable: true,
    },
  ], []);

  // Default column properties
  const defaultColDef = useMemo(() => ({
    resizable: true,
    sortable: true,
    filter: true,
  }), []);

  if (isLoading) {
    return (
      <div className="bg-white shadow-md rounded-lg p-6 mb-8">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Keyword Research</h2>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded mb-4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="bg-white shadow-md rounded-lg p-6 mb-8">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Keyword Research</h2>
        <p className="text-gray-500 italic">No data available. Please submit the form to generate keyword research.</p>
      </div>
    );
  }

  return (
    <div className="bg-white shadow-md rounded-lg p-6 mb-8">
      <h2 className="text-xl font-semibold text-gray-800 mb-4">Keyword Research</h2>
      
      <div className="ag-theme-alpine" style={{ height: '500px', width: '100%' }}>
        <AgGridReact
          rowData={rowData}
          columnDefs={columnDefs}
          defaultColDef={defaultColDef}
          pagination={true}
          paginationPageSize={20}
          domLayout="normal"
          suppressCellFocus={true}
          rowSelection="multiple"
          animateRows={true}
          enableCellTextSelection={true}
          ensureDomOrder={true}
        />
      </div>
    </div>
  );
};

export default KeywordResearchAGGrid;
