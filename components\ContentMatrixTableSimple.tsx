import React from 'react';

interface ContentMatrixItem {
  mainKeyword: string;
  cluster: string;
  contentType: string;
  focus: string;
  category: string;
  url: string;
  searchVol: number;
  keywords: string;
  status: string;
}

interface ContentMatrixTableSimpleProps {
  data: ContentMatrixItem[];
  isLoading?: boolean;
}

const ContentMatrixTableSimple: React.FC<ContentMatrixTableSimpleProps> = ({ data, isLoading = false }) => {
  if (isLoading) {
    return (
      <div className="bg-white shadow-md rounded-lg p-6 mb-8">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Content Cluster</h2>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded mb-4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="bg-white shadow-md rounded-lg p-6 mb-8">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Content Cluster</h2>
        <p className="text-gray-500 italic">No data available. Please submit the form to generate content matrix.</p>
      </div>
    );
  }

  return (
    <div className="bg-white shadow-md rounded-lg p-6 mb-8">
      <h2 className="text-xl font-semibold text-gray-800 mb-4">Content Cluster</h2>
      <div className="text-sm text-gray-600 mb-4">
        Showing {data.length} content pieces
      </div>

      {/* Desktop view - traditional table */}
      <div className="hidden md:block overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">S.No.</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Main Keyword</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cluster</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Content Type</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Focus</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">URL</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Search Vol</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Keywords</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {data.map((item, index) => (
              <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">{index + 1}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.mainKeyword}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.cluster}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.contentType}</td>
                <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">{item.focus}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.category}</td>
                <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                  {item.url ? (
                    <a href={item.url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 underline">
                      {item.url}
                    </a>
                  ) : (
                    item.url
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.searchVol?.toLocaleString() || '0'}</td>
                <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">{item.keywords}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    item.status === 'Published'
                      ? 'bg-green-100 text-green-800'
                      : item.status === 'Draft'
                      ? 'bg-yellow-100 text-yellow-800'
                      : item.status === 'Planned'
                      ? 'bg-blue-100 text-blue-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {item.status}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Mobile view - card layout */}
      <div className="md:hidden space-y-4">
        <div className="text-sm text-gray-500 mb-2 flex justify-between items-center">
          <span>Showing {data.length} results</span>
          <div className="text-indigo-600 text-xs">
            Swipe cards horizontally to see more →
          </div>
        </div>

        {data.map((item, index) => (
          <div key={index} className="bg-white border border-gray-200 rounded-lg shadow-sm p-4">
            <div className="flex flex-wrap justify-between items-center mb-3">
              <div className="flex items-center space-x-2">
                <span className="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded-full font-bold">#{index + 1}</span>
                <h3 className="font-medium text-gray-900">{item.focus}</h3>
              </div>
              <div className="flex space-x-2 mt-1 sm:mt-0">
                <span className="text-xs bg-indigo-100 text-indigo-800 px-2 py-1 rounded-full">{item.contentType}</span>
                <span className={`text-xs px-2 py-1 rounded-full ${
                  item.status === 'Published'
                    ? 'bg-green-100 text-green-800'
                    : item.status === 'Draft'
                    ? 'bg-yellow-100 text-yellow-800'
                    : 'bg-gray-100 text-gray-800'
                }`}>{item.status}</span>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3 text-sm">
              <div>
                <span className="block text-gray-500 text-xs">Main Keyword</span>
                <span className="font-medium">{item.mainKeyword}</span>
              </div>
              <div>
                <span className="block text-gray-500 text-xs">Cluster</span>
                <span className="font-medium">{item.cluster}</span>
              </div>
              <div>
                <span className="block text-gray-500 text-xs">Category</span>
                <span className="font-medium">{item.category}</span>
              </div>
              <div>
                <span className="block text-gray-500 text-xs">Search Vol</span>
                <span className="font-medium">{item.searchVol?.toLocaleString() || '0'}</span>
              </div>
            </div>

            {item.keywords && (
              <div className="mt-3">
                <span className="block text-gray-500 text-xs">Keywords</span>
                <span className="text-sm text-gray-900">{item.keywords}</span>
              </div>
            )}

            {item.url && (
              <div className="mt-3">
                <span className="block text-gray-500 text-xs">URL</span>
                <a href={item.url} target="_blank" rel="noopener noreferrer" className="text-sm text-blue-600 hover:text-blue-800 underline break-all">
                  {item.url}
                </a>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default ContentMatrixTableSimple;
